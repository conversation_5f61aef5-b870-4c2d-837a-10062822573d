﻿﻿﻿﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Shared.Entities;

namespace TwentyDishes.Api.Services
{
    public class RecipeService : Repository<Recipe>, IRecipeService
    {
        private readonly IIngredientService ingredientService;
        private readonly IIdGenerationService _idGenerationService;

        public RecipeService(IIngredientService ingredientService, IIdGenerationService idGenerationService, IDbContext dbContext) : base(dbContext)
        {
            this.ingredientService = ingredientService;
            _idGenerationService = idGenerationService;
        }

        public async Task<List<UserWeek>> GenerateUserWeekRecipes(User currentUser, int newWeeksNeeded, DateTime latestWeekStartDate)
        {
            List<Recipe> breakfasts = new List<Recipe>();
            List<Recipe> dinners = new List<Recipe>();
            List<Recipe> dinnerSides = new List<Recipe>();
            List<Recipe> lunches = new List<Recipe>();
            UserWeek newUserWeek;
            Random random = new Random();
            List<UserWeek> result = new List<UserWeek>();
            List<Recipe> snacks = new List<Recipe>();

            HashSet<string> acceptableIngr = ingredientService.QueryIngredients().Where(x => !currentUser.UserFoodCategoryExclusions.Contains(x.FoodCategory)).Select(x => x.Name).ToHashSet(StringComparer.InvariantCultureIgnoreCase);
            List<Recipe> acceptableRecipes = await Task.FromResult(
                GetTable().AsEnumerable()
                    .Where( r => 
                        !r.Ingredients.Any(y => currentUser.UserIngredientExclusions.Contains(r.Name, StringComparer.InvariantCultureIgnoreCase))
                        && r.Ingredients.All(y => acceptableIngr.Contains(y.Name))
                        && r.Diets.Any(y => currentUser.UserDiets.Contains(y))
                        && (r.UserId == "-1" || (currentUser.Id != null && r.UserId == currentUser.Id))
                    )
                    .ToList());

            if (currentUser.MealSelection.BreakfastEnabled)
            {
                breakfasts = acceptableRecipes.Where(x => x.Meals.Contains("Breakfast")).ToList();
            }
            if (currentUser.MealSelection.DinnerEnabled)
            {
                dinners = acceptableRecipes.Where(x => x.Meals.Contains("Dinner") && x.Courses.Contains("Main Course")).ToList();

                dinnerSides = acceptableRecipes.Where(x => x.Meals.Contains("Dinner") && x.Courses.Contains("Side Dish")).ToList();
            }
            if (currentUser.MealSelection.LunchEnabled)
            {
                lunches = acceptableRecipes.Where(x => x.Meals.Contains("Lunch") && x.Courses.Contains("Main Course")).ToList();
            }
            if (currentUser.MealSelection.SnacksEnabled)
            {
                snacks = acceptableRecipes.Where(x => !x.Courses.Contains("Main Course")).ToList();
            }

            List<string> CreateDinner()
            {
                var result = new List<string>();

                if (dinners.Any())
                {
                    result.Add(dinners.ElementAt(random.Next(dinners.Count)).Id);
                }

                if (dinnerSides.Any())
                {
                    result.Add(dinnerSides.ElementAt(random.Next(dinnerSides.Count)).Id);
                }

                return result;
            }

            for (int i = 0; i < newWeeksNeeded; i++)
            {
                int dayMultiplier = i + 1;
                newUserWeek = new UserWeek()
                {
                    FridayBreakfast = breakfasts.Any() ? new List<string> { breakfasts.ElementAt(random.Next(breakfasts.Count)).Id } : new List<string>(),
                    FridayDinner = CreateDinner(),
                    FridayLunch = lunches.Any() ? new List<string> { lunches.ElementAt(random.Next(lunches.Count)).Id } : new List<string>(),
                    FridaySnacks = snacks.Any() ? new List<string> { snacks.ElementAt(random.Next(snacks.Count)).Id } : new List<string>(),
                    MondayBreakfast = breakfasts.Any() ? new List<string> { breakfasts.ElementAt(random.Next(breakfasts.Count)).Id } : new List<string>(),
                    MondayDinner = CreateDinner(),
                    MondayLunch = lunches.Any() ? new List<string> { lunches.ElementAt(random.Next(lunches.Count)).Id } : new List<string>(),
                    MondaySnacks = snacks.Any() ? new List<string> { snacks.ElementAt(random.Next(snacks.Count)).Id } : new List<string>(),
                    SaturdayBreakfast = breakfasts.Any() ? new List<string> { breakfasts.ElementAt(random.Next(breakfasts.Count)).Id } : new List<string>(),
                    SaturdayDinner = CreateDinner(),
                    SaturdayLunch = lunches.Any() ? new List<string> { lunches.ElementAt(random.Next(lunches.Count)).Id } : new List<string>(),
                    SaturdaySnacks = snacks.Any() ? new List<string> { snacks.ElementAt(random.Next(snacks.Count)).Id } : new List<string>(),
                    StartDate = latestWeekStartDate.AddDays(7 * dayMultiplier).ToUniversalTime().Date,
                    StopDate = latestWeekStartDate.AddDays((7 * dayMultiplier) + 6).ToUniversalTime().Date,
                    SundayBreakfast = breakfasts.Any() ? new List<string> { breakfasts.ElementAt(random.Next(breakfasts.Count)).Id } : new List<string>(),
                    SundayDinner = CreateDinner(),
                    SundayLunch = lunches.Any() ? new List<string> { lunches.ElementAt(random.Next(lunches.Count)).Id } : new List<string>(),
                    SundaySnacks = snacks.Any() ? new List<string> { snacks.ElementAt(random.Next(snacks.Count)).Id } : new List<string>(),
                    ThursdayBreakfast = breakfasts.Any() ? new List<string> { breakfasts.ElementAt(random.Next(breakfasts.Count)).Id } : new List<string>(),
                    ThursdayDinner = CreateDinner(),
                    ThursdayLunch = lunches.Any() ? new List<string> { lunches.ElementAt(random.Next(lunches.Count)).Id } : new List<string>(),
                    ThursdaySnacks = snacks.Any() ? new List<string> { snacks.ElementAt(random.Next(snacks.Count)).Id } : new List<string>(),
                    TuesdayBreakfast = breakfasts.Any() ? new List<string> { breakfasts.ElementAt(random.Next(breakfasts.Count)).Id } : new List<string>(),
                    TuesdayDinner = CreateDinner(),
                    TuesdayLunch = lunches.Any() ? new List<string> { lunches.ElementAt(random.Next(lunches.Count)).Id } : new List<string>(),
                    TuesdaySnacks = snacks.Any() ? new List<string> { snacks.ElementAt(random.Next(snacks.Count)).Id } : new List<string>(),
                    WednesdayBreakfast = breakfasts.Any() ? new List<string> { breakfasts.ElementAt(random.Next(breakfasts.Count)).Id } : new List<string>(),
                    WednesdayDinner = CreateDinner(),
                    WednesdayLunch = lunches.Any() ? new List<string> { lunches.ElementAt(random.Next(lunches.Count)).Id } : new List<string>(),
                    WednesdaySnacks = snacks.Any() ? new List<string> { snacks.ElementAt(random.Next(snacks.Count)).Id } : new List<string>()
                };
                result.Add(newUserWeek);
            }

            return result;
        }

        public async Task<List<RecipePartial>> GetAllPartialRecipes(string userId = null)
        {
            return await GetTable()
                .Where(r => r.UserId == "-1" || (userId != null && r.UserId == userId))
                .Select(r => new RecipePartial()
                {
                    RecipeDiets = r.Diets,
                    RecipeId = r.Id,
                    RecipeImageId = r.CloudflareImageId,
                    RecipeName = r.Name,
                })
                .OrderBy(x => x.RecipeName)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<List<Recipe>> GetAllRecipes(string userId = null)
        {
            return await GetTable()
                .Where(r => r.UserId == "-1" || (userId != null && r.UserId == userId))
                .OrderBy(x => x.Name)
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<string> GetNextAvailableId()
        {
            return await _idGenerationService.GenerateNextIdAsync<Recipe>("Recipes");
        }

        public async Task<List<RecipePartial>> GetPartialRecipesByName(string keyword, string userId = null)
        {
            return await Task.FromResult(
                GetTable()
                    .Where(r => r.Name.ToLower().Contains(keyword) && (r.UserId == "-1" || (userId != null && r.UserId == userId)))
                    .Select(r => new RecipePartial()
                    {
                        RecipeDiets = r.Diets,
                        RecipeId = r.Id,
                        RecipeImageId = r.CloudflareImageId,
                        RecipeName = r.Name,
                    })
                    .OrderBy(x => x.RecipeName)
                    .ToList()
            );
        }

        public async Task<Recipe> GetRecipe(string pk, string userId = null)
        {
            return await GetTable()
                .Where(r => r.Pk == pk && (r.UserId == "-1" || (userId != null && r.UserId == userId)))
                .AsNoTracking()
                .FirstOrDefaultAsync();
        }

        public async Task<List<Recipe>> GetRecipes(List<string> recipesToFetch, string userId = null)
        {
            return await Task.FromResult(
                GetTable()
                    .Where(r => recipesToFetch.Contains(r.Pk) && (r.UserId == "-1" || (userId != null && r.UserId == userId)))
                    .ToList());
        }

        public async Task<List<Recipe>> GetRecipesByName(string keyword, string userId = null)
        {
            return await Task.FromResult(
                GetTable()
                    .Where(r => r.Name.Contains(keyword, StringComparison.InvariantCultureIgnoreCase) && (r.UserId == "-1" || (userId != null && r.UserId == userId)))
                    .ToList());
        }

        public async Task<List<Recipe>> GetWeeklyRecipes(UserWeek userWeek, string userId = null)
        {
            List<string> combinedList = userWeek.SundayBreakfast.Concat(userWeek.SundayDinner).Concat(userWeek.SundayLunch).Concat(userWeek.SundaySnacks)
                                        .Concat(userWeek.MondayBreakfast).Concat(userWeek.MondayDinner).Concat(userWeek.MondayLunch).Concat(userWeek.MondaySnacks)
                                        .Concat(userWeek.TuesdayBreakfast).Concat(userWeek.TuesdayDinner).Concat(userWeek.TuesdayLunch).Concat(userWeek.TuesdaySnacks)
                                        .Concat(userWeek.WednesdayBreakfast).Concat(userWeek.WednesdayDinner).Concat(userWeek.WednesdayLunch).Concat(userWeek.WednesdaySnacks)
                                        .Concat(userWeek.ThursdayBreakfast).Concat(userWeek.ThursdayDinner).Concat(userWeek.ThursdayLunch).Concat(userWeek.ThursdaySnacks)
                                        .Concat(userWeek.FridayBreakfast).Concat(userWeek.FridayDinner).Concat(userWeek.FridayLunch).Concat(userWeek.FridaySnacks)
                                        .Concat(userWeek.SaturdayBreakfast).Concat(userWeek.SaturdayDinner).Concat(userWeek.SaturdayLunch).Concat(userWeek.SaturdaySnacks).ToList();

            var result = await Task.FromResult(
                GetTable()
                    .Where(r => combinedList.Contains(r.Id) && (r.UserId == "-1" || (userId != null && r.UserId == userId)))
                    .ToList());
            
            return result;
        }

        public async Task InsertRecipe(Recipe modified)
        {
            await Insert(modified);
        }

        public async Task UpdateRecipe(Recipe modified)
        {
            await Update(modified);
        }
    }
}