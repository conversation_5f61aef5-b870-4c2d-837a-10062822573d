using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Linq;
using System.Threading.Tasks;
using TwentyDishes.Api.Services.ServiceInterfaces;

namespace TwentyDishes.Api.Services
{
    public class IdGenerationService : IIdGenerationService
    {
        private readonly IDbContext _dbContext;
        private readonly ILogger<IdGenerationService> _logger;

        public IdGenerationService(IDbContext dbContext, ILogger<IdGenerationService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        /// <summary>
        /// Generates the next available sequential ID for a given entity type.
        /// This method handles edge cases properly and uses proper async patterns.
        /// </summary>
        /// <typeparam name="TEntity">The entity type</typeparam>
        /// <param name="containerName">The container name for logging purposes</param>
        /// <returns>The next available ID as a string</returns>
        public async Task<string> GenerateNextIdAsync<TEntity>(string containerName) where TEntity : class
        {
            try
            {
                // Get the maximum ID directly from the database without loading all records
                var maxIdQuery = _dbContext.Set<TEntity>()
                    .Select(GetPkSelector<TEntity>())
                    .Where(pk => pk != null && pk != "");

                long maxId = 0;

                if (await maxIdQuery.AnyAsync())
                {
                    var maxIdString = await maxIdQuery.MaxAsync();
                    if (!string.IsNullOrEmpty(maxIdString) && long.TryParse(maxIdString, out var parsedMaxId))
                    {
                        maxId = parsedMaxId;
                    }
                }

                var nextId = maxId + 1;
                _logger.LogInformation("Generated next ID {NextId} for container {ContainerName}", nextId, containerName);

                return nextId.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating next ID for container {ContainerName}", containerName);

                // Fallback to timestamp-based ID if sequential generation fails
                return await GenerateTimestampIdAsync();
            }
        }

        /// <summary>
        /// Generates a GUID-based ID. Recommended for new implementations.
        /// </summary>
        /// <returns>A new GUID as a string</returns>
        public async Task<string> GenerateGuidIdAsync()
        {
            var guid = Guid.NewGuid().ToString();
            _logger.LogDebug("Generated GUID ID: {GuidId}", guid);
            return await Task.FromResult(guid);
        }

        /// <summary>
        /// Generates a timestamp-based ID for fallback scenarios.
        /// </summary>
        /// <returns>A timestamp-based ID as a string</returns>
        public async Task<string> GenerateTimestampIdAsync()
        {
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var timestampId = timestamp.ToString();
            _logger.LogDebug("Generated timestamp ID: {TimestampId}", timestampId);
            return await Task.FromResult(timestampId);
        }

        /// <summary>
        /// Gets the appropriate property selector for the Pk field based on entity type.
        /// This method uses reflection to safely access the Pk property.
        /// </summary>
        private static System.Linq.Expressions.Expression<Func<TEntity, string>> GetPkSelector<TEntity>() where TEntity : class
        {
            var entityType = typeof(TEntity);
            var parameter = System.Linq.Expressions.Expression.Parameter(entityType, "entity");

            // Look for a property named "Pk"
            var pkProperty = entityType.GetProperty("Pk");
            if (pkProperty != null && pkProperty.PropertyType == typeof(string))
            {
                var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, pkProperty);
                return System.Linq.Expressions.Expression.Lambda<Func<TEntity, string>>(propertyAccess, parameter);
            }

            // Fallback: look for "Id" property
            var idProperty = entityType.GetProperty("Id");
            if (idProperty != null && idProperty.PropertyType == typeof(string))
            {
                var propertyAccess = System.Linq.Expressions.Expression.Property(parameter, idProperty);
                return System.Linq.Expressions.Expression.Lambda<Func<TEntity, string>>(propertyAccess, parameter);
            }

            // If no suitable property found, throw an exception
            throw new InvalidOperationException($"Entity type {entityType.Name} does not have a suitable Pk or Id property of type string.");
        }
    }
}
