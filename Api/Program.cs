using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Azure.Functions.Worker.Extensions.OpenApi.Extensions;using System;
//using System.Linq;
using TwentyDishes.Api;
using TwentyDishes.Api.Services.ServiceInterfaces;
using TwentyDishes.Api.Services;

// Assembly assembly = AppDomain.CurrentDomain.GetAssemblies().SingleOrDefault(x => x.GetName().Name == "Api");

// List<Type> TypesToRegister = assembly.ExportedTypes
//                                       .Where(x => !string.IsNullOrEmpty(x.Namespace))
//                                       .Where(x => x.IsClass && !x.IsInterface)
//                                       .Where(x => x.Namespace.StartsWith("TwentyDishes.Api.Services", false, System.Globalization.CultureInfo.InvariantCulture)).ToList();

var host = new HostBuilder()
    .ConfigureFunctionsWebApplication()
    .ConfigureOpenApi()
    .ConfigureServices(services =>
    {
        services.AddApplicationInsightsTelemetryWorkerService();
        services.ConfigureFunctionsApplicationInsights();

        Syncfusion.Licensing.SyncfusionLicenseProvider.RegisterLicense(Environment.GetEnvironmentVariable("SyncfusionDotNetRegKey"));

        services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
        services.AddScoped<IDbContext, DbContext>();
        services.AddScoped<IIdGenerationService, IdGenerationService>();
        services.AddScoped(typeof(IAuthService), typeof(AuthService));
        services.AddScoped(typeof(IAuth0Service), typeof(Auth0Service));
        services.AddScoped(typeof(ICloudflareService), typeof(CloudflareService));

        //Register all services dynamically
        // foreach (Type type in TypesToRegister)
        // {
        //     services.AddScoped(type.GetInterface($"I{type.Name}"), type);
        // }

        services.AddScoped(typeof(IIngredientService), typeof(IngredientService));
        services.AddScoped(typeof(IMetaService), typeof(MetaService));
        services.AddScoped(typeof(IPaddleService), typeof(PaddleService));
        services.AddScoped(typeof(IRecipeService), typeof(RecipeService));
        services.AddScoped(typeof(IUserService), typeof(UserService));

        services.AddHttpClient();
    })
    .Build();

host.Run();