using System;

namespace TwentyDishes.Api.Helpers
{
    /// <summary>
    /// Helper class for ID generation recommendations and utilities.
    /// </summary>
    public static class IdGenerationHelper
    {
        /// <summary>
        /// Generates a GUID-based ID with a prefix for better readability.
        /// Recommended for new entities to avoid sequential ID issues.
        /// </summary>
        /// <param name="prefix">Optional prefix for the ID (e.g., "recipe_", "user_")</param>
        /// <returns>A prefixed GUID string</returns>
        public static string GeneratePrefixedGuid(string prefix = "")
        {
            var guid = Guid.NewGuid().ToString("N"); // No hyphens
            return string.IsNullOrEmpty(prefix) ? guid : $"{prefix}{guid}";
        }

        /// <summary>
        /// Generates a short ID using a combination of timestamp and random characters.
        /// Good for user-facing IDs that need to be shorter than GUIDs.
        /// </summary>
        /// <param name="length">Length of the random part (default: 8)</param>
        /// <returns>A short ID string</returns>
        public static string GenerateShortId(int length = 8)
        {
            const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var random = new Random();
            var timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString("X"); // Hex timestamp
            
            var randomPart = new char[length];
            for (int i = 0; i < length; i++)
            {
                randomPart[i] = chars[random.Next(chars.Length)];
            }
            
            return $"{timestamp}{new string(randomPart)}";
        }

        /// <summary>
        /// Validates if a string can be parsed as a valid sequential ID.
        /// </summary>
        /// <param name="id">The ID to validate</param>
        /// <returns>True if the ID is a valid sequential number</returns>
        public static bool IsValidSequentialId(string id)
        {
            return !string.IsNullOrEmpty(id) && long.TryParse(id, out var result) && result > 0;
        }
    }
}
